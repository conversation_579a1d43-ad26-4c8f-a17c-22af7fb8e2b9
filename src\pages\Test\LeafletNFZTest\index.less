.leaflet-nfz-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;

  .test-header {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .test-description {
      margin-bottom: 12px;

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .engine-info {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .test-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    min-height: 0;

    .map-container {
      flex: 1;
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }
    }

    .info-panel {
      width: 300px;
      flex-shrink: 0;

      .test-info {
        margin-bottom: 16px;

        p {
          margin: 8px 0;
          font-size: 14px;
          color: #666;

          strong {
            color: #333;
          }
        }
      }

      .nfz-statistics {
        h4 {
          margin: 0 0 12px 0;
          color: #1890ff;
          font-size: 14px;
          font-weight: 600;
        }

        p {
          margin: 8px 0;
          font-size: 14px;
          color: #333;
        }

        > div {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          margin-top: 8px;
        }
      }

      .test-instructions {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        h4 {
          margin: 0 0 12px 0;
          color: #1890ff;
          font-size: 14px;
          font-weight: 600;
        }

        ol {
          margin: 0;
          padding-left: 20px;

          li {
            margin: 4px 0;
            font-size: 13px;
            color: #666;
            line-height: 1.4;
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .leaflet-nfz-test .test-content {
    flex-direction: column;

    .info-panel {
      width: 100%;
      order: -1;
    }

    .map-container {
      height: 500px;
    }
  }
}

@media (max-width: 768px) {
  .leaflet-nfz-test {
    .test-header {
      padding: 12px;
    }

    .test-content {
      padding: 12px;
      gap: 12px;

      .map-container {
        height: 400px;
      }
    }
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .leaflet-nfz-test {
    background: #141414;

    .test-header {
      background: #1f1f1f;
      border-bottom-color: #434343;
    }

    .test-content .info-panel .test-info p {
      color: #ccc;

      strong {
        color: #fff;
      }
    }
  }
}
