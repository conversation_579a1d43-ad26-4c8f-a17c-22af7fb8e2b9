/**
 * 禁飞区按钮组件
 * 独立的浮动按钮，用于控制禁飞区的显示/隐藏
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Tooltip, message } from 'antd';
import { EnvironmentOutlined, LoadingOutlined, ExclamationCircleOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { detectMapEngine, isMapReady } from '@/utils/mapEngineDetector';
import { getCurrentViewBounds, calculateBoundsDifference } from '@/utils/mapBoundsHelper';
import { fetchNFZData } from '@/services/nfzService';
import styles from './index.module.less';
import NFZIcon from '@/assets/img/NFZ.png';

/**
 * 禁飞区按钮组件
 * @param {Object} props - 组件属性
 * @param {Object} props.mapInstance - 地图实例（Leaflet或Cesium）
 * @param {Function} props.onNFZDataLoad - 禁飞区数据加载回调
 * @param {Function} props.onNFZToggle - 禁飞区显示切换回调
 * @param {Object} props.options - 可选配置
 * @param {string} props.position - 按钮位置 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
 * @param {Object} props.customStyle - 自定义样式对象
 * @param {Object} props.customButtonStyle - 自定义按钮样式对象
 * @param {Object} props.customStatsStyle - 自定义统计信息样式对象
 * @param {string} props.className - 自定义CSS类名
 * @param {string} props.size - 按钮大小 'small' | 'middle' | 'large'
 */
const NFZButton = ({
  mapInstance,
  onNFZDataLoad,
  onNFZToggle,
  options = {},
  position = 'bottom-right',
  customStyle = {},
  customButtonStyle = {},
  customStatsStyle = {},
  className = '',
  size = 'large',
}) => {
  // 状态管理
  const [isVisible, setIsVisible] = useState(false); // 禁飞区是否显示
  const [isLoading, setIsLoading] = useState(false); // 是否正在加载
  const [hasError, setHasError] = useState(false); // 是否有错误
  const [nfzData, setNfzData] = useState(null); // 禁飞区数据
  const [lastBounds, setLastBounds] = useState(null); // 上次的边界

  // 引用
  const updateTimeoutRef = useRef(null);
  const mapEngineRef = useRef(null);
  const handleMapMoveEndRef = useRef(null);

  // 配置选项
  const config = {
    updateDelay: 1500, // 相机停止移动后延迟更新时间（毫秒）
    boundsThreshold: 0.2, // 边界变化阈值（20%）
    minZoomLevel: 10, // 最小显示缩放级别
    maxZoomLevel: 18, // 最大显示缩放级别
    ...options,
  };

  // 初始化
  useEffect(() => {
    if (mapInstance) {
      // 检测地图引擎类型
      const engineType = detectMapEngine(mapInstance);
      mapEngineRef.current = engineType;
      console.log('NFZButton: Detected map engine:', engineType);

      // 确保地图实例已准备好
      if (!isMapReady(mapInstance)) {
        console.warn('NFZButton: Map instance is not ready yet');
        return;
      }

      // 根据isVisible状态决定是否添加事件监听
      if (isVisible) {
        addMapEventListeners();
      } else {
        removeMapEventListeners();
      }
      
      // 重置状态
      setLastBounds(null);
      setNfzData(null);
      setHasError(false);
      
      // 如果当前是可见状态，立即更新数据
      if (isVisible) {
        updateNFZData();
      }
    }

    return () => {
      // 清理
      removeMapEventListeners();
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [mapInstance, isVisible]); // 添加isVisible作为依赖项

  // 添加地图事件监听
  const addMapEventListeners = () => {
    if (!mapInstance) return;
    
    // 确保地图实例已准备好
    if (!isMapReady(mapInstance)) {
      console.warn('NFZButton: Map is not ready for adding event listeners');
      return;
    }

    const engine = mapEngineRef.current;
    
    // 检查handleMapMoveEndRef.current是否为函数
    if (!handleMapMoveEndRef.current || typeof handleMapMoveEndRef.current !== 'function') {
      return;
    }

    try {
      if (engine === 'cesium') {
        // 确保Cesium实例有效
        if (mapInstance.isDestroyed && mapInstance.isDestroyed()) {
          console.warn('NFZButton: Cesium viewer is destroyed');
          return;
        }
        
        // Cesium 相机移动事件
        if (mapInstance.camera && mapInstance.camera.moveEnd) {
          mapInstance.camera.moveEnd.addEventListener(handleMapMoveEndRef.current);
        } else {
          console.warn('NFZButton: Cesium camera or moveEnd event not available');
        }
      } else if (engine === 'leaflet') {
        // 确保Leaflet实例有效
        if (!mapInstance._loaded) {
          console.warn('NFZButton: Leaflet map is not loaded');
          return;
        }
        
        // Leaflet 地图移动事件
        mapInstance.on('moveend', handleMapMoveEndRef.current);
        mapInstance.on('zoomend', handleMapMoveEndRef.current);
      } else {
        console.warn('NFZButton: Unknown map engine type:', engine);
      }
    } catch (error) {
      console.error('NFZButton: Error adding event listeners:', error);
    }
  };

  // 移除地图事件监听
  const removeMapEventListeners = () => {
    if (!mapInstance) return;

    const engine = mapEngineRef.current;
    
    // 检查handleMapMoveEndRef.current是否为函数
    if (!handleMapMoveEndRef.current || typeof handleMapMoveEndRef.current !== 'function') {
      return;
    }

    try {
      if (engine === 'cesium') {
        // 确保Cesium实例有效
        if (mapInstance.isDestroyed && !mapInstance.isDestroyed() && 
            mapInstance.camera && mapInstance.camera.moveEnd) {
          mapInstance.camera.moveEnd.removeEventListener(handleMapMoveEndRef.current);
        }
      } else if (engine === 'leaflet') {
        // 确保Leaflet实例有效
        if (mapInstance._loaded) {
          mapInstance.off('moveend', handleMapMoveEndRef.current);
          mapInstance.off('zoomend', handleMapMoveEndRef.current);
        }
      }
    } catch (error) {
      console.error('NFZButton: Error removing event listeners:', error);
    }
  };


  // 检查是否需要更新数据
  const shouldUpdateData = useCallback(
    currentBounds => {
      if (!lastBounds) return true;

      // 计算边界差异
      const difference = calculateBoundsDifference(lastBounds, currentBounds);
      return difference > config.boundsThreshold;
    },
    [lastBounds, config.boundsThreshold],
  );

  // 检查缩放级别是否在显示范围内
  const isZoomLevelValid = useCallback(() => {
    if (!mapInstance) return false;

    const engine = mapEngineRef.current;

    if (engine === 'cesium') {
      // Cesium 通过相机高度判断缩放级别
      const height = mapInstance.camera.positionCartographic?.height || 0;
      // 简单的高度到缩放级别转换（可以根据需要调整）
      const zoomLevel = Math.max(1, Math.min(18, 18 - Math.log2(height / 1000)));
      return zoomLevel >= config.minZoomLevel && zoomLevel <= config.maxZoomLevel;
    } else if (engine === 'leaflet') {
      const zoomLevel = mapInstance.getZoom();
      return zoomLevel >= config.minZoomLevel && zoomLevel <= config.maxZoomLevel;
    }

    return true;
  }, [mapInstance, config.minZoomLevel, config.maxZoomLevel]);

  // 更新禁飞区数据
  const updateNFZData = useCallback(async () => {
    if (!mapInstance || !isMapReady(mapInstance)) {
      console.warn('NFZButton: Map is not ready');
      return;
    }

    // 检查缩放级别
    if (!isZoomLevelValid()) {
      console.log('NFZButton: Zoom level out of range, hiding NFZ');
      setNfzData(null);
      onNFZDataLoad && onNFZDataLoad(null);
      return;
    }

    try {
      // 获取当前可见边界
      const currentBounds = getCurrentViewBounds(mapInstance, mapEngineRef.current);
      if (!currentBounds) {
        console.warn('NFZButton: Unable to get current bounds');
        return;
      }

      // 检查是否需要更新
      if (!shouldUpdateData(currentBounds)) {
        return;
      }

      setIsLoading(true);
      setHasError(false);

      // 获取禁飞区数据
      const data = await fetchNFZData(currentBounds, options);

      setNfzData(data);
      setLastBounds(currentBounds);

      // 通知父组件
      onNFZDataLoad && onNFZDataLoad(data);

    } catch (error) {
      console.error('NFZButton: Error updating NFZ data:', error);
      setHasError(true);
      message.error('获取禁飞区数据失败');
    } finally {
      setIsLoading(false);
    }
  }, [mapInstance, options, onNFZDataLoad, shouldUpdateData, isZoomLevelValid]);

  // 处理地图移动结束事件
  const handleMapMoveEnd = useCallback(() => {
    // 清除之前的延时器
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // 延迟更新（相机停止移动后1.5秒）
    updateTimeoutRef.current = setTimeout(() => {
      updateNFZData();
    }, config.updateDelay);
  }, [config.updateDelay, updateNFZData]);

  // 更新handleMapMoveEnd引用
  useEffect(() => {
    // 确保handleMapMoveEndRef.current是一个函数
    if (typeof handleMapMoveEnd === 'function') {
      handleMapMoveEndRef.current = handleMapMoveEnd;
    } else {
      console.log('NFZButton: handleMapMoveEnd is not a function:', handleMapMoveEnd);
    }
  }, [handleMapMoveEnd]);

  // 切换禁飞区显示
  const toggleNFZ = async () => {
    const newVisible = !isVisible;
    setIsVisible(newVisible);

    // 通知父组件状态变化
    onNFZToggle && onNFZToggle(newVisible);

    if (newVisible) {
      // 显示禁飞区，立即加载数据
      await updateNFZData();
    } else {
      // 隐藏禁飞区，清除数据
      setNfzData(null);
      setLastBounds(null);
      // 确保传递null数据到父组件，触发渲染器清除和可见性设置
      onNFZDataLoad && onNFZDataLoad(null);
    }
  };

  // 获取按钮图标
  const getButtonIcon = () => {
    if (isLoading) {
      return <LoadingOutlined spin />;
    }
    if (hasError) {
      return <ExclamationCircleOutlined />;
    }
    if (isVisible) {
      return <img src={NFZIcon} alt="NFZ Icon" style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }} />;
    }
    return <img src={NFZIcon} alt="NFZ Icon" style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }} />;
  };

  // 获取按钮状态
  const getButtonType = () => {
    if (hasError) return 'danger';
    if (isVisible) return 'primary';
    return 'default';
  };

  // 获取提示文本
  const getTooltipTitle = () => {
    if (isLoading) return '正在加载禁飞区数据...';
    if (hasError) return '加载禁飞区数据失败，点击重试';
    if (isVisible) return '点击隐藏禁飞区';
    return '点击显示禁飞区';
  };

  // 获取按钮位置样式
  const getPositionStyle = () => {
    const baseStyle = {
      position: 'absolute',
      zIndex: 1000,
    };

    let positionStyle = {};
    switch (position) {
      case 'bottom-right':
        positionStyle = { bottom: '20px', right: '20px' };
        break;
      case 'bottom-left':
        positionStyle = { bottom: '20px', left: '20px' };
        break;
      case 'top-right':
        positionStyle = { top: '20px', right: '20px' };
        break;
      case 'top-left':
        positionStyle = { top: '20px', left: '20px' };
        break;
      default:
        positionStyle = { bottom: '20px', right: '20px' };
    }

    // 合并自定义样式
    return { ...baseStyle, ...positionStyle, ...customStyle };
  };

  // 获取按钮样式
  const getButtonStyle = () => {
    return { ...customButtonStyle };
  };

  // 获取统计信息样式
  const getStatsStyle = () => {
    return { ...customStatsStyle };
  };

  // 获取按钮类名
  const getButtonClassName = () => {
    return `${styles.nfzButton} ${className}`.trim();
  };

  // 如果没有地图实例，不渲染按钮
  if (!mapInstance) {
    return null;
  }

  return (
    <div style={getPositionStyle()}>
      <Tooltip title={getTooltipTitle()} placement="left">
        <Button
          type={getButtonType()}
          shape="circle"
          size={size}
          icon={getButtonIcon()}
          onClick={toggleNFZ}
          disabled={isLoading}
          className={getButtonClassName()}
          style={getButtonStyle()}
        />
      </Tooltip>

      {/* 数据统计显示（可选） */}
      {/* {isVisible && nfzData && nfzData.data && nfzData.data.areas && (
        <div className={styles.nfzStats} style={getStatsStyle()}>
          <span>{nfzData.data.areas.length} 个禁飞区</span>
        </div>
      )} */}
    </div>
  );
};

export default NFZButton;
