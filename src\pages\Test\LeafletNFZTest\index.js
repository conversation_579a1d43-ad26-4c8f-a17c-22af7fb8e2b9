import React, { useState, useRef, useEffect } from 'react';
import { Card, message, Tag } from 'antd';
import { Map<PERSON>ontainer, TileLayer, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { createLeafletNFZRenderer } from '@/utils/leafletNFZRenderer';
import { processNFZData } from '@/utils/nfzDataProcessor';
import { detectMapEngine } from '@/utils/mapEngineDetector';
import NFZButton from '@/components/NFZButton';
import './index.less';

// 解决 Leaflet Marker 图标未显示的问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

// 地图初始化组件
const MapInitializer = ({ onMapReady }) => {
  const map = useMap();
  const initializedRef = useRef(false);

  useEffect(() => {
    if (map && onMapReady && !initializedRef.current) {
      onMapReady(map);
      initializedRef.current = true;
    }
  }, [map, onMapReady]);

  return null;
};

const LeafletNFZTest = () => {
  const mapRef = useRef(null);
  const nfzRendererRef = useRef(null);
  const [nfzData, setNfzData] = useState([]);
  const [mapInstance, setMapInstance] = useState(null);
  const [engineInfo, setEngineInfo] = useState(null);

  // 地图中心点（北京）
  const center = [39.9042, 116.4074];
  const zoom = 12;

  // 天地图底图
  const tiandituUrl = "http://t0.tianditu.gov.cn/img_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";

  // 天地图标注
  const tiandituLabelUrl = "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";

  // 地图创建回调
  const handleMapCreated = (map) => {
    console.log('Leaflet地图创建成功:', map);
    setMapInstance(map);
    mapRef.current = map;

    // 检测地图引擎
    const engine = detectMapEngine(map);
    setEngineInfo(engine);
    console.log('地图引擎检测结果:', engine);

    // 初始化NFZ渲染器
    try {
      const renderer = createLeafletNFZRenderer(map);
      renderer.setClickCallback((nfzData, event) => {
        console.log('NFZ clicked:', nfzData, event);
      });
      nfzRendererRef.current = renderer;
      console.log('Leaflet NFZ渲染器初始化成功');
    } catch (error) {
      console.error('NFZ渲染器初始化失败:', error);
      message.error('NFZ渲染器初始化失败');
    }
  };

  // NFZ按钮数据加载回调
  const handleNFZDataLoad = (data) => {
    if (data && nfzRendererRef.current) {
      try {
        const processed = processNFZData(data);
        nfzRendererRef.current.renderNFZAreas(processed, {
          showLabels: false,
          enableClick: true,
          maxDisplayCount: 100
        });
        setNfzData(processed);
      } catch (error) {
        console.error('渲染禁飞区失败:', error);
      }
    } else if (nfzRendererRef.current) {
      // 清除禁飞区
      nfzRendererRef.current.clearNFZAreas();
      // 设置可见性为false
      nfzRendererRef.current.setVisible(false);
      setNfzData([]);
    }
  };

  // NFZ按钮切换回调
  const handleNFZToggle = (visible) => {
    console.log('NFZ visibility toggled:', visible);
    
    // 确保渲染器可见性与按钮状态同步
    if (nfzRendererRef.current) {
      nfzRendererRef.current.setVisible(visible);
    }
  };

  // 统计信息
  const getStatistics = () => {
    if (!nfzData || nfzData.length === 0) return null;

    const levelCounts = {};
    nfzData.forEach(area => {
      const level = area.level;
      levelCounts[level] = (levelCounts[level] || 0) + 1;
    });

    return (
      <div className="nfz-statistics">
        <h4>禁飞区统计</h4>
        <p>总数: {nfzData.length}</p>
        <div>
          {Object.entries(levelCounts).map(([level, count]) => (
            <Tag key={level} color={getLevelColor(level)}>
              级别{level}: {count}个
            </Tag>
          ))}
        </div>
      </div>
    );
  };

  // 获取级别颜色
  const getLevelColor = (level) => {
    const colors = {
      '0': 'red',
      '1': 'volcano',
      '2': 'orange',
      '3': 'gold',
      '7': 'green',
      '8': 'blue',
      '10': 'purple'
    };
    return colors[level] || 'default';
  };

  return (
    <div className="leaflet-nfz-test">
      <div className="test-header">
        <Card title="Leaflet 禁飞区渲染器测试" size="small">
          <div className="test-description">
            <p>本页面测试Leaflet地图引擎的禁飞区渲染功能。点击右下角的禁飞区按钮来显示/隐藏禁飞区。</p>
          </div>

          {engineInfo && (
            <div className="engine-info">
              <Tag color="blue">引擎: {engineInfo.engine}</Tag>
              <Tag color="green">版本: {engineInfo.version}</Tag>
              <Tag color="orange">状态: {engineInfo.ready ? '就绪' : '未就绪'}</Tag>
            </div>
          )}
        </Card>
      </div>

      <div className="test-content">
        <div className="map-container">
          <MapContainer
            center={center}
            zoom={zoom}
            style={{ height: '100%', width: '100%' }}
            zoomControl={false}
            attributionControl={false}
          >
            <MapInitializer onMapReady={handleMapCreated} />
            <TileLayer
              url={tiandituUrl}
              maxZoom={18}
              attribution="&copy; 天地图"
            />
            <TileLayer
              url={tiandituLabelUrl}
              maxZoom={18}
            />
          </MapContainer>

          {/* NFZ按钮 */}
          {mapInstance && (
            <NFZButton
              mapInstance={mapInstance}
              onNFZDataLoad={handleNFZDataLoad}
              onNFZToggle={handleNFZToggle}
              position="bottom-right"
              customStyle={{ bottom: '20px', right: '20px' }}
            />
          )}
        </div>

        <div className="info-panel">
          <Card title="测试信息" size="small">
            <div className="test-info">
              <p><strong>地图类型:</strong> Leaflet</p>
              <p><strong>中心点:</strong> {center.join(', ')}</p>
              <p><strong>缩放级别:</strong> {zoom}</p>
              <p><strong>渲染器状态:</strong> {nfzRendererRef.current ? '已初始化' : '未初始化'}</p>
              <p><strong>地图实例:</strong> {mapInstance ? '已创建' : '未创建'}</p>
            </div>

            {getStatistics()}

            <div className="test-instructions">
              <h4>测试说明</h4>
              <ol>
                <li>点击右下角的禁飞区按钮</li>
                <li>等待禁飞区数据加载</li>
                <li>观察地图上的禁飞区渲染效果</li>
                <li>点击禁飞区查看详情弹窗</li>
                <li>再次点击按钮隐藏禁飞区</li>
              </ol>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LeafletNFZTest;
